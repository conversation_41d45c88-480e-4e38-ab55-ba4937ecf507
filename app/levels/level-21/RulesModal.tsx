"use client";

import { useEffect, useRef } from "react";
import { HelpCircle, Eye, CheckCircle, Lightbulb, Keyboard as KeyboardIcon, Target } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface Props {
  open: boolean;
  onClose: () => void;
}

export default function RulesModal({ open, onClose }: Props) {
  const closeBtnRef = useRef<HTMLButtonElement | null>(null);
  const overlayRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!open) return;

    // Focus the primary close button for accessibility
    closeBtnRef.current?.focus();

    const onKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, [open, onClose]);

  if (!open) return null;

  return (
    <div
      ref={overlayRef}
      className="fixed inset-0 z-[100] flex items-center justify-center p-4"
      aria-hidden={!open}
    >
      <div
        className="fixed inset-0 bg-black/70 backdrop-blur-sm"
        onClick={() => onClose()}
      />
      <Card
        role="dialog"
        aria-modal="true"
        aria-labelledby="level-rules-title"
        className="relative max-w-3xl w-full z-[110] transform rounded-lg shadow-xl"
      >
        <CardHeader className="flex items-center justify-between gap-4 px-6 py-5">
          <div className="flex items-center gap-3">
            <div className="bg-blue-50 rounded-full p-2">
              <HelpCircle className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <CardTitle id="level-rules-title" className="text-xl font-bold">
                Level 21 — Brand Logo Recognition
              </CardTitle>
              <div className="text-sm text-gray-600">Identify famous brand logos</div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="px-6 pb-6 space-y-4">
          {/* Challenge Overview */}
          <section className="space-y-2">
            <h3 className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-100">
                <Eye className="h-4 w-4 mr-1" />
                Recognition Challenge
              </Badge>
            </h3>
            <p className="text-sm text-gray-600">
              Test your brand recognition skills! You'll be shown 10 famous brand logos from various 
              industries. Your task is to correctly identify each brand by typing its name. This challenge 
              tests your knowledge of global brands and visual recognition abilities.
            </p>
          </section>

          {/* Game Rules */}
          <section className="space-y-2">
            <h4 className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-100">
                <Target className="h-4 w-4 mr-1" />
                How to Play
              </Badge>
            </h4>

            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li><strong>Total Questions:</strong> 10 brand logo identification challenges</li>
              <li><strong>Input Method:</strong> Type brand names using the virtual keyboard or your physical keyboard</li>
              <li><strong>Answer Flexibility:</strong> Multiple variations accepted (e.g., "Under Armor" or "Under Armour")</li>
              <li><strong>Case Insensitive:</strong> Answers are not case-sensitive</li>
              <li><strong>Character Limit:</strong> Maximum 50 characters per answer</li>
            </ul>
          </section>

          {/* Virtual Keyboard */}
          <section className="space-y-2">
            <h4 className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-100">
                <KeyboardIcon className="h-4 w-4 mr-1" />
                Virtual Keyboard
              </Badge>
            </h4>

            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Use the on-screen keyboard to type brand names</li>
              <li>Click letters to add them to your answer</li>
              <li>Use "Space" button to add spaces between words</li>
              <li>Use "Backspace" (⌫) to delete characters</li>
              <li>You can also use your physical keyboard if preferred</li>
            </ul>
          </section>

          {/* Scoring System */}
          <section className="space-y-2">
            <h4 className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-100">
                <CheckCircle className="h-4 w-4 mr-1" />
                Scoring System
              </Badge>
            </h4>

            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li><strong>Base Score:</strong> 100 points per correct answer</li>
              <li><strong>Hint Penalty:</strong> -25 points if you use a hint</li>
              <li><strong>Accuracy Bonus:</strong> +100 points for 6+ correct, +200 points for 8+ correct</li>
              <li><strong>Perfect Score Bonus:</strong> +300 points for getting all 10 correct</li>
              <li><strong>Maximum Possible Score:</strong> 1,500 points (10 correct without hints + bonuses)</li>
            </ul>
          </section>

          {/* Hint System */}
          <section className="space-y-2">
            <h4 className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-100">
                <Lightbulb className="h-4 w-4 mr-1" />
                Hint System
              </Badge>
            </h4>

            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Each logo has a helpful hint available</li>
              <li>Hints provide context about the brand without giving away the answer</li>
              <li>Using a hint costs 25 points from that question's score</li>
              <li>Hints describe the industry, product type, or notable characteristics</li>
              <li>Use hints strategically to maximize your score</li>
            </ul>
          </section>

          {/* Strategy Tips */}
          <section className="space-y-2">
            <h4 className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-100">
                <Target className="h-4 w-4 mr-1" />
                Pro Tips
              </Badge>
            </h4>

            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Look carefully at logo colors, shapes, and design elements</li>
              <li>Consider the industry the brand might be from</li>
              <li>Think about both the full company name and common abbreviations</li>
              <li>Don't overthink - often your first instinct is correct</li>
              <li>Use hints if you're completely stuck rather than guessing randomly</li>
            </ul>
          </section>

          {/* Brand Categories */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Eye className="h-5 w-5 text-blue-600" />
              <h4 className="font-semibold text-blue-700">Brand Categories</h4>
            </div>
            <p className="text-sm text-blue-600">
              The logos include brands from various industries: technology companies, search engines, 
              luxury goods, fast food chains, gaming consoles, beverages, sportswear, social media 
              platforms, and financial services. This diverse mix tests your knowledge across different sectors.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-3">
            <Button 
              variant="outline"
              onClick={onClose}
              className="border-gray-300 text-gray-600 hover:bg-gray-50"
            >
              Close Rules
            </Button>
            
            <Button 
              ref={closeBtnRef}
              onClick={onClose} 
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white px-8"
            >
              <Eye className="h-4 w-4 mr-2" />
              Start Recognition Challenge
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
